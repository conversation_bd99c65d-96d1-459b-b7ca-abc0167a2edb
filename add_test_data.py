#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para agregar datos de prueba para curiosidades e interacciones
"""

import pymysql
import os

# Cargar variables de entorno si existe .env
try:
    from dotenv import load_dotenv
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    load_dotenv(env_path)
except ImportError:
    pass

def get_db_config():
    """Obtener configuración de base de datos"""
    database_url = os.environ.get('DATABASE_URL')
    if database_url:
        import re
        match = re.match(r'mysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', database_url)
        if match:
            user, password, host, port, database = match.groups()
            return {
                'host': host,
                'user': user,
                'password': password,
                'database': database,
                'port': int(port),
                'charset': 'utf8mb4',
                'cursorclass': pymysql.cursors.DictCursor
            }
    
    return {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'user': os.environ.get('DB_USER', 'root'),
        'password': os.environ.get('DB_PASSWORD', ''),
        'database': os.environ.get('DB_NAME', 'VerdeQR'),
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }

def add_test_data():
    """Agregar datos de prueba"""
    try:
        # Conectar a la base de datos
        config = get_db_config()
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("=== AGREGANDO DATOS DE PRUEBA ===")
        
        # Agregar curiosidades para la especie 3 (Palma de Cera del Quindío)
        curiosidades_especie3 = [
            "La Palma de Cera del Quindío es el árbol nacional de Colombia y puede vivir hasta 200 años. Es la palma más alta del mundo, alcanzando hasta 60 metros de altura.",
            "Sus hojas pueden medir hasta 5 metros de longitud y se utilizan tradicionalmente en la celebración del Domingo de Ramos en Colombia."
        ]
        
        for curiosidad in curiosidades_especie3:
            cursor.execute('''
                INSERT INTO CuriosidadesArbol (Especie, Descripcion, Estado)
                VALUES (%s, %s, %s)
            ''', (3, curiosidad, 1))
            print(f"✓ Curiosidad agregada para especie 3")
        
        # Agregar interacciones para la especie 3 (Palma de Cera del Quindío)
        interacciones_especie3 = [
            ("Refugio de fauna", "Proporciona refugio y sitios de anidación para diversas especies de aves, especialmente loros y pericos que utilizan sus cavidades naturales."),
            ("Relación con el loro orejiamarillo", "Mantiene una relación simbiótica especial con el loro orejiamarillo (Ognorhynchus icterotis), especie endémica que depende de esta palma para anidar y alimentarse.")
        ]
        
        for tipo, descripcion in interacciones_especie3:
            cursor.execute('''
                INSERT INTO InteraccionesEcologicas (Especie, TipoInteraccion, Descripcion, Estado)
                VALUES (%s, %s, %s, %s)
            ''', (3, tipo, descripcion, 1))
            print(f"✓ Interacción '{tipo}' agregada para especie 3")
        
        # Agregar curiosidades para la especie 5 (Ceiba)
        curiosidades_especie5 = [
            "La Ceiba es considerada sagrada por muchas culturas indígenas americanas. Se cree que conecta el mundo subterráneo, la tierra y el cielo a través de sus raíces, tronco y copa.",
            "Su tronco puede alcanzar hasta 3 metros de diámetro y desarrolla contrafuertes impresionantes que pueden extenderse hasta 10 metros desde la base."
        ]
        
        for curiosidad in curiosidades_especie5:
            cursor.execute('''
                INSERT INTO CuriosidadesArbol (Especie, Descripcion, Estado)
                VALUES (%s, %s, %s)
            ''', (5, curiosidad, 1))
            print(f"✓ Curiosidad agregada para especie 5")
        
        # Agregar interacciones para la especie 5 (Ceiba)
        interacciones_especie5 = [
            ("Polinización nocturna", "Sus flores se abren durante la noche y son polinizadas principalmente por murciélagos nectarívoros, estableciendo una relación mutualista crucial."),
            ("Ecosistema epífito", "Su corteza lisa y ramas horizontales proporcionan soporte para una gran diversidad de plantas epífitas, creando jardines aéreos complejos.")
        ]
        
        for tipo, descripcion in interacciones_especie5:
            cursor.execute('''
                INSERT INTO InteraccionesEcologicas (Especie, TipoInteraccion, Descripcion, Estado)
                VALUES (%s, %s, %s, %s)
            ''', (5, tipo, descripcion, 1))
            print(f"✓ Interacción '{tipo}' agregada para especie 5")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n✅ Datos de prueba agregados exitosamente!")
        print("Ahora puedes ver árboles de las especies 3 (Palma de Cera) y 5 (Ceiba) para verificar que aparezcan las curiosidades e interacciones.")
        
    except Exception as e:
        print(f"❌ Error al agregar datos: {str(e)}")

if __name__ == "__main__":
    add_test_data()
