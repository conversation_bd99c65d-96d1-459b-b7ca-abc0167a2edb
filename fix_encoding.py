#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corregir la codificación de caracteres en la base de datos VerdeQR
Ejecutar este script para solucionar el error de codificación UTF-8
"""

import pymysql
import os
import sys

# Cargar variables de entorno si existe .env
try:
    from dotenv import load_dotenv
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    load_dotenv(env_path)
except ImportError:
    pass

def get_db_config():
    """Obtener configuración de base de datos"""
    database_url = os.environ.get('DATABASE_URL')
    if database_url:
        import re
        match = re.match(r'mysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', database_url)
        if match:
            user, password, host, port, database = match.groups()
            return {
                'host': host,
                'user': user,
                'password': password,
                'database': database,
                'port': int(port),
                'charset': 'utf8mb4'
            }
    
    return {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'user': os.environ.get('DB_USER', 'root'),
        'password': os.environ.get('DB_PASSWORD', ''),
        'database': os.environ.get('DB_NAME', 'VerdeQR'),
        'charset': 'utf8mb4'
    }

def fix_database_encoding():
    """Corregir la codificación de la base de datos"""
    try:
        # Conectar a la base de datos
        config = get_db_config()
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("Conectado a la base de datos. Iniciando corrección de codificación...")
        
        # Lista de comandos SQL para ejecutar
        sql_commands = [
            "ALTER DATABASE VerdeQR CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Arbol CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Arbol MODIFY COLUMN Caracteristicas TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Arbol MODIFY COLUMN ServiciosEcosistemicos TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Arbol MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Especie CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Especie MODIFY COLUMN NombreCientifico VARCHAR(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Especie MODIFY COLUMN NombreVulgar VARCHAR(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE UsoArbol CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE CuriosidadesArbol CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE CuriosidadesArbol MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE InteraccionesEcologicas CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE InteraccionesEcologicas MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE TipoBosque CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE TipoBosque MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Centro CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
            "ALTER TABLE Usuario CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
        ]
        
        # Ejecutar cada comando
        for i, command in enumerate(sql_commands, 1):
            try:
                print(f"Ejecutando comando {i}/{len(sql_commands)}: {command[:50]}...")
                cursor.execute(command)
                connection.commit()
                print(f"✓ Comando {i} ejecutado exitosamente")
            except Exception as e:
                print(f"⚠ Error en comando {i}: {str(e)}")
                # Continuar con el siguiente comando
                continue
        
        # Verificar los cambios
        print("\nVerificando cambios...")
        cursor.execute("""
            SELECT TABLE_NAME, TABLE_COLLATION
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = 'VerdeQR' AND TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """)
        
        tables = cursor.fetchall()
        print("\nEstado de las tablas:")
        for table in tables:
            print(f"  {table[0]}: {table[1]}")
        
        # Verificar columnas específicas de la tabla Arbol
        cursor.execute("""
            SELECT COLUMN_NAME, CHARACTER_SET_NAME, COLLATION_NAME
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = 'VerdeQR' 
            AND TABLE_NAME = 'Arbol'
            AND COLUMN_NAME IN ('Caracteristicas', 'ServiciosEcosistemicos', 'Descripcion')
        """)
        
        columns = cursor.fetchall()
        print("\nColumnas de texto en tabla Arbol:")
        for column in columns:
            print(f"  {column[0]}: {column[1]} / {column[2]}")
        
        print("\n✅ Corrección de codificación completada exitosamente!")
        print("Ahora puedes intentar actualizar el árbol nuevamente.")
        
    except Exception as e:
        print(f"❌ Error al corregir la codificación: {str(e)}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
    
    return True

if __name__ == "__main__":
    print("=== Corrección de Codificación UTF-8 para VerdeQR ===")
    print("Este script corregirá la codificación de caracteres en la base de datos.")
    
    respuesta = input("¿Deseas continuar? (s/n): ").lower().strip()
    if respuesta in ['s', 'si', 'sí', 'y', 'yes']:
        fix_database_encoding()
    else:
        print("Operación cancelada.")
