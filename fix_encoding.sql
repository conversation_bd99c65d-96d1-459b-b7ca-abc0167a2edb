-- Script para corregir la codificación de caracteres en la base de datos VerdeQR
-- Ejecutar este script para solucionar el error de codificación UTF-8

USE VerdeQR;

-- 1. Cambiar la codificación de la base de datos
ALTER DATABASE VerdeQR CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. Cambiar la codificación de la tabla Arbol y sus columnas
ALTER TABLE Arbol CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. Cambiar específicamente las columnas de texto que pueden contener caracteres especiales
ALTER TABLE Arbol MODIFY COLUMN Caracteristicas TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Arbol MODIFY COLUMN ServiciosEcosistemicos TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Arbol MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 4. Cambiar otras tablas importantes que pueden contener texto con caracteres especiales
ALTER TABLE Especie CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Especie MODIFY COLUMN NombreCientifico VARCHAR(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Especie MODIFY COLUMN NombreVulgar VARCHAR(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE UsoArbol CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoArbol MODIFY COLUMN Nombre VARCHAR(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoArbol MODIFY COLUMN Categoria VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE UsoMedicinal CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoMedicinal MODIFY COLUMN ParteUtilizada VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoMedicinal MODIFY COLUMN Preparacion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoMedicinal MODIFY COLUMN EnfermedadesTratadas TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE UsoComestible CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoComestible MODIFY COLUMN ParteComestible VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoComestible MODIFY COLUMN FormaConsumo TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE UsoComestible MODIFY COLUMN ValorNutricional TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE CuriosidadesArbol CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE CuriosidadesArbol MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE InteraccionesEcologicas CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE InteraccionesEcologicas MODIFY COLUMN TipoInteraccion VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE InteraccionesEcologicas MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE TipoBosque CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE TipoBosque MODIFY COLUMN Nombre VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE TipoBosque MODIFY COLUMN Descripcion TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE Centro CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Centro MODIFY COLUMN NombreCentro VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Centro MODIFY COLUMN Direccion VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE Usuario CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Usuario MODIFY COLUMN Nombre VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE Usuario MODIFY COLUMN Correo VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 5. Verificar que los cambios se aplicaron correctamente
SELECT 
    TABLE_NAME,
    TABLE_COLLATION
FROM 
    information_schema.TABLES 
WHERE 
    TABLE_SCHEMA = 'VerdeQR' 
    AND TABLE_TYPE = 'BASE TABLE';

-- 6. Verificar las columnas específicas
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM 
    information_schema.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'VerdeQR' 
    AND TABLE_NAME = 'Arbol'
    AND COLUMN_NAME IN ('Caracteristicas', 'ServiciosEcosistemicos', 'Descripcion');
