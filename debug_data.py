#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar los datos en las tablas de curiosidades e interacciones
"""

import pymysql
import os

# Cargar variables de entorno si existe .env
try:
    from dotenv import load_dotenv
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    load_dotenv(env_path)
except ImportError:
    pass

def get_db_config():
    """Obtener configuración de base de datos"""
    database_url = os.environ.get('DATABASE_URL')
    if database_url:
        import re
        match = re.match(r'mysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', database_url)
        if match:
            user, password, host, port, database = match.groups()
            return {
                'host': host,
                'user': user,
                'password': password,
                'database': database,
                'port': int(port),
                'charset': 'utf8mb4'
            }
    
    return {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'user': os.environ.get('DB_USER', 'root'),
        'password': os.environ.get('DB_PASSWORD', ''),
        'database': os.environ.get('DB_NAME', 'VerdeQR'),
        'charset': 'utf8mb4'
    }

def debug_data():
    """Verificar datos en las tablas"""
    try:
        # Conectar a la base de datos
        config = get_db_config()
        config['cursorclass'] = pymysql.cursors.DictCursor
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("=== VERIFICACIÓN DE DATOS ===")
        
        # Verificar especies
        cursor.execute("SELECT IDEspecie, NombreCientifico, NombreVulgar FROM Especie")
        especies = cursor.fetchall()
        print(f"\n📊 ESPECIES REGISTRADAS: {len(especies)}")
        for especie in especies:
            print(f"  - ID: {especie['IDEspecie']}, Científico: {especie['NombreCientifico']}, Vulgar: {especie['NombreVulgar']}")
        
        # Verificar árboles
        cursor.execute("SELECT IDArbol, Especie FROM Arbol")
        arboles = cursor.fetchall()
        print(f"\n🌳 ÁRBOLES REGISTRADOS: {len(arboles)}")
        for arbol in arboles:
            print(f"  - Árbol ID: {arbol['IDArbol']}, Especie ID: {arbol['Especie']}")
        
        # Verificar curiosidades
        cursor.execute("SELECT * FROM CuriosidadesArbol")
        curiosidades = cursor.fetchall()
        print(f"\n🤔 CURIOSIDADES REGISTRADAS: {len(curiosidades)}")
        for curiosidad in curiosidades:
            print(f"  - ID: {curiosidad['IDCuriosidad']}, Especie: {curiosidad['Especie']}, Estado: {curiosidad['Estado']}")
            print(f"    Descripción: {curiosidad['Descripcion'][:100]}...")
        
        # Verificar interacciones
        cursor.execute("SELECT * FROM InteraccionesEcologicas")
        interacciones = cursor.fetchall()
        print(f"\n🌿 INTERACCIONES ECOLÓGICAS REGISTRADAS: {len(interacciones)}")
        for interaccion in interacciones:
            print(f"  - ID: {interaccion['IDInteraccion']}, Especie: {interaccion['Especie']}, Estado: {interaccion['Estado']}")
            print(f"    Tipo: {interaccion['TipoInteraccion']}")
            print(f"    Descripción: {interaccion['Descripcion'][:100]}...")
        
        # Verificar relaciones específicas para un árbol
        if arboles:
            arbol_test = arboles[0]
            arbol_id = arbol_test['IDArbol']
            especie_id = arbol_test['Especie']
            
            print(f"\n🔍 VERIFICACIÓN ESPECÍFICA PARA ÁRBOL {arbol_id} (Especie {especie_id}):")
            
            # Curiosidades para esta especie
            cursor.execute("SELECT * FROM CuriosidadesArbol WHERE Especie = %s AND Estado = 1", (especie_id,))
            curiosidades_especie = cursor.fetchall()
            print(f"  - Curiosidades activas: {len(curiosidades_especie)}")
            
            # Interacciones para esta especie
            cursor.execute("SELECT * FROM InteraccionesEcologicas WHERE Especie = %s AND Estado = 1", (especie_id,))
            interacciones_especie = cursor.fetchall()
            print(f"  - Interacciones activas: {len(interacciones_especie)}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error al verificar datos: {str(e)}")

if __name__ == "__main__":
    debug_data()
